
<template>
  <div class="fileUpload">
    <div style="display: flex; width: 100%">
      <div class="title-area">
        <span class="data-source-name">{{ datasourceName }}</span>
        <span style="font-size: 18px; margin-left: 20px">文件上传</span>
      </div>
      <el-steps style="flex: 0.5; margin-left: 25%; margin-top: 20px" :active="stepsActive" process-status="finish"
        finish-status="success">
        <el-step title="文件上传" />
        <el-step title="预览数据" />
      </el-steps>
    </div>
    <!-- 第一个步骤 -->
    <div class="fileUploadContent" v-if="stepsActive == 0">
      <el-form ref="fileUploadRef" class="fileUploadForm" :model="fileUploadForm" label-width="auto"
        :rules="fileUploadRules" label-position="top">
        <el-form-item label="数据源名称" prop="dataName">
          <el-input v-model.number="fileUploadForm.dataName" type="text" autocomplete="off" />
        </el-form-item>
      </el-form>
      <el-upload style="width: 100%" action="#" :on-change="handleChange" :file-list="fileList" drag multiple :limit="1"
        :auto-upload="false" :on-progress="handleProgress" :show-file-list="true">
        <el-icon class="el-icon--upload"><img style="width: 48px; height: 44px"
            src="@/assets/imgs/datadempement/uploadArea.png" mode="scaleToFill" /></el-icon>
        <div class="el-upload__text" style="font-weight: 400; font-size: 14px">
          <em>点击</em>
          将文件拖到此处次区域上传
        </div>
        <div class="el-upload__text" style="font-size: 12px; color: #999">文件只支持.csv、.xlsx、.xls格式</div>
        <template #file="{ file }">
          <div class="upload-fileList">
            <div class="upload-fileList-info">
              <img src="@/assets/imgs/datadempement/uploadFile.png" alt="" class="upload-fileList-img" />
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">{{ formatFileSize(file.size) }}</span>
            </div>
            <div class="file-actions">
              <el-icon class="delete-icon" @click.stop="handleDeleteFile(file)">
                <Close />
              </el-icon>
            </div>
          </div>
        </template>
      </el-upload>
      <div></div>
      <div style="width: 100%; margin-top: 50px">
        <span style="display: inline-flex; align-items: center; gap: 4px; font-size: 16px">
          温馨提示
          <el-icon style="color: #0d6ce4">
            <WarningFilled />
          </el-icon>
        </span>
        <ul style="list-style: none">
          <li>1.仅支持上传结构化数据，以便于我们更好的识别。有合并的单元格的，请处理后再上传</li>
          <li>2.系统会默认将上传的文件首行作为标题行，第二行开始作为要上传的数据</li>
          <li>3.若上传的表格Sheet页首行为空或者整体内容为空会上传失败</li>
          <li>4.最多支持5个Sheet的解析和上传，若您需要上传超过5个Sheet的内容，请拆分为多个Excel文件</li>
          <li>5.Sheet表名不能重复，请确保每个Sheet表名唯一</li>
          <li>6.文件大小不超过50 MB</li>
        </ul>
      </div>

      <div style="
          position: absolute;
          bottom: 20px;
          right: 20px;
          display: flex;
          justify-content: flex-end;
        ">
        <el-button type="primary" @click="uploadNext(fileUploadRef)">下一步</el-button>
      </div>
    </div>
    <!-- 第二个步骤 -->
    <div class="fileUploadContentSecond" v-if="stepsActive == 1">
      <div class="sheetoptionsBody">
        <div class="sheetTabs" :style="{ background: acitveSheet == item.id ? '#fff' : '' }" v-for="item in sheetList"
          :key="item.sheetName" @click="acitveSheetClick(item, $event)">
          <el-checkbox v-model="item.checked"></el-checkbox>
          <span class="sheetTabsName" :style="{ color: acitveSheet == item.id ? '#2744d6' : '' }">{{
            item.sheetName
          }}</span>
          <el-popover class="box-item" :content="item.errorMsg" placement="top-start" v-if="item.errorMsg">
            <template #reference>
              <el-icon style="color: #faad14; margin-left: 5px">
                <WarningFilled />
              </el-icon>
            </template>
          </el-popover>
        </div>
      </div>
      <GeneralTables 
      :columns="previewColumns" 
      :data="sheetData" 
      :border="false" 
      :stripe="false" 
      :pagination="false"
      :table-key="tableKey" 
      style="margin-top: 20px">
        <template v-for="(column, index) in previewColumns" :key="`${column.prop}-${index}`" #[`header-${column.prop}`]>
          <div style="display: flex; align-items: center; gap: 5px">
            <el-select v-model="column.selectValue" placeholder="请选择" style="width: 80px"
              @change="(value) => handleSelectChange(column, value)">
              <el-option v-for="selectItem in fileTypeList" :key="selectItem.label" :label="selectItem.label"
                :value="selectItem.label"></el-option>
            </el-select>
            <el-input v-model="column.inputValue" :readonly="column.readonly" @dblclick="toggleEditable(column)"
              style="width: 50%"></el-input>
          </div>
        </template>
      </GeneralTables>
      <div style="
          position: absolute;
          bottom: 20px;
          right: 20px;
          display: flex;
          justify-content: flex-end;
        ">
        <el-button type="primary" @click="okAndUpload(fileUploadRef)">确定并上传</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onBeforeMount, onMounted, getCurrentInstance,computed } from 'vue'
import GeneralTables from '@/components/GeneralTables/index.vue'
import { ElMessage } from 'element-plus'
import { dataUpload,dataSave } from '@/api/datainfor/dataupload.js'
import { Close, WarningFilled } from '@element-plus/icons-vue'
const emit = defineEmits(['close', 'update-success'])
// ------文件上传第一步----------
let stepsActive = ref(0)
let fileUploadRef = ref(null)
let fileUploadForm = reactive({
  dataName: ''
})
let fileList = ref([])
let fileUploadRules = reactive({
  dataName: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }]
})

// ------文件上传第二步-------------
let sheetList = ref([])
let sheetListData = ref([
  {
    id: '1',
    sheetName: '生产经营汇总表-sheet1',
    checked: true,
    errorMsg: '',
    // 预览数据表格列配置
    previewColumns: [
      {
        prop: 'date',
        // align: 'center',
        // headerSlot: true,
        selectValue: '数值',
        inputValue: '111'
        // readonly: true,
      },
      {
        prop: 'name',
        // align: 'center',
        // headerSlot: true,
        selectValue: '数值',
        inputValue: '111'
        // readonly: true,
      },
      {
        prop: 'address',
        // align: 'center',
        // headerSlot: true,
        selectValue: '数值',
        inputValue: '111'
        // readonly: true,
      }
    ],
    sheetData: [
      {
        date: '2016-05-03',
        name: 'Tom',
        address: 'No. 189, Grove St, Los Angeles'
      },
      {
        date: '2016-05-02',
        name: 'Tom',
        address: 'No. 189, Grove St, Los Angeles'
      }
    ]
  },
  {
    id: '2',
    sheetName: '生产经营汇总表-sheet2',
    checked: true,
    errorMsg: '该sheep页数据为空',
    previewColumns: [
      {
        prop: 'date',
        // align: 'center',
        // headerSlot: true,
        selectValue: '日期',
        inputValue: '222'
        // readonly: true,
      },
      {
        prop: 'name',
        // align: 'center',
        // headerSlot: true,
        selectValue: '日期',
        inputValue: '222'
        // readonly: true,
      },
      {
        prop: 'address',
        // align: 'center',
        // headerSlot: true,
        selectValue: '日期',
        inputValue: '222'
        // readonly: true,
      }
    ],
    sheetData: [
      {
        date: '2016-05-04',
        name: 'Tom1',
        address: 'No. 189, Grove St, Los Angeles'
      },
      {
        date: '2016-05-01',
        name: 'Tom1',
        address: 'No. 189, Grove St, Los Angeles'
      }
    ]
  },
  {
    id: '3',
    sheetName: '生产经营汇总表-sheet3',
    checked: true,
    errorMsg: '该sheep页数据为空',
    previewColumns: [
      {
        prop: 'date',
        // align: 'center',
        // headerSlot: true,
        selectValue: '文本',
        inputValue: '333'
        // readonly: true,
      },
      {
        prop: 'name',
        // align: 'center',
        // headerSlot: true,
        selectValue: '文本',
        inputValue: '333'
        // readonly: true,
      },
      {
        prop: 'address',
        // align: 'center',
        // headerSlot: true,
        selectValue: '文本',
        inputValue: '333'
        // readonly: true,
      }
    ],
    sheetData: [
      {
        date: '2016-06-04',
        name: 'Tom3',
        address: 'No. 189, Grove St, Los Angeles'
      },
      {
        date: '2016-06-01',
        name: 'Tom3',
        address: 'No. 189, Grove St, Los Angeles'
      }
    ]
  }
])
let fileTypeList = ref([
  {
    label: '数值'
  },
  {
    label: '日期'
  },
  {
    label: '文本'
  }
])
let acitveSheet = ref('0')
// 添加一个ref来存储当前激活的列配置
const currentColumns = ref([])
// 用于强制刷新表格
const tableKey = ref(0)

const fileName = ref('')
const fileSize = ref(0)
const filePath = ref('')
const datasourceName = ref('')

// 前端文件预检查函数
const preValidateFile = async (file) => {
  return new Promise((resolve) => {
    // 对于Excel文件，我们可以进行一些基础检查
    if (file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.type === 'application/vnd.ms-excel') {

      // 检查文件名是否包含可能的问题指示
      const fileName = file.name.toLowerCase();
      if (fileName.includes('合并') || fileName.includes('merge')) {
        ElMessage.warning('文件名提示可能包含合并单元格，请检查文件内容');
      }

      // 这里可以添加更多的前端检查逻辑
      // 由于浏览器限制，我们主要依赖后端API的检查结果
    }
    resolve(true);
  });
};

const handleChange = async (file, uploadFiles) => {
  console.log('文件选择变化', file, uploadFiles)
  if (!beforeUpload(file.raw)) {
    return
  }

  // 进行前端预检查
  await preValidateFile(file.raw);

  fileList.value = uploadFiles // 直接使用传入的 uploadFiles 更新响应式引用
}
// 上传进度回调
const handleProgress = (event, file) => {
  uploadProgress.value = Math.round(event.percent)
}
// 上传的文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}
// 文件上传前的校验
const beforeUpload = (file) => {
  const isCSV = file.type === 'text/csv'
  const isExcel =
    file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel'
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isCSV && !isExcel) {
    ElMessage.error('只能上传 CSV、XLSX、XLS 文件!')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过 50MB!')
    return false
  }
  return true
}
// 删除文件
const handleDeleteFile = (file) => {
  const index = fileList.value.indexOf(file)
  fileList.value.splice(index, 1)
}
// 验证文件上传的函数
const validateFileUpload = (sheetList) => {
  console.log('开始验证文件上传，sheetList:', sheetList);

  // 1. 检查是否有合并单元格 - 多种检测方式
  const hasMergedCells = sheetList.some(sheet => {
    console.log(`检查Sheet: ${sheet.sheetName}, errorMsg: ${sheet.errorMsg}`);
    // 检查errorMsg中的合并单元格关键字
    if (sheet.errorMsg && (
      sheet.errorMsg.includes('合并单元格') ||
      sheet.errorMsg.includes('merged') ||
      sheet.errorMsg.includes('merge') ||
      sheet.errorMsg.toLowerCase().includes('merged cells')
    )) {
      console.log(`发现合并单元格错误信息: ${sheet.errorMsg}`);
      return true;
    }

    // 检查数据结构异常 - 如果有合并单元格，通常会导致数据结构不规整
    if (sheet.sheetData && sheet.sheetData.length > 0) {
      const firstRowKeys = Object.keys(sheet.sheetData[0] || {});
      console.log(`Sheet ${sheet.sheetName} 的列名:`, firstRowKeys);

      // 检查是否有空的列名或者异常的列名
      const hasEmptyOrInvalidColumns = firstRowKeys.some(key =>
        !key || key.trim() === '' || key.includes('__EMPTY') || key.startsWith('EXCEL_')
      );
      if (hasEmptyOrInvalidColumns) {
        console.log(`发现异常列名，可能存在合并单元格`);
        return true;
      }

      // 检查数据行是否有明显的结构不一致（可能由合并单元格导致）
      const hasInconsistentStructure = sheet.sheetData.some(row => {
        const rowKeys = Object.keys(row || {});
        return rowKeys.length !== firstRowKeys.length ||
               rowKeys.some(key => !firstRowKeys.includes(key));
      });
      if (hasInconsistentStructure) {
        console.log(`发现数据结构不一致，可能存在合并单元格`);
        return true;
      }
    }

    return false;
  });

  if (hasMergedCells) {
    console.log('检测到合并单元格，阻止上传');
    ElMessage.error('Excel文件有合并单元格，请处理后再上传');
    return false;
  }

  console.log('合并单元格检查通过');

  // 额外检查：如果没有检测到合并单元格，但数据看起来可疑，给出警告
  const suspiciousSheets = sheetList.filter(sheet => {
    if (!sheet.sheetData || sheet.sheetData.length === 0) return false;

    // 检查是否有很多空值（可能是合并单元格导致的）
    const totalCells = sheet.sheetData.length * Object.keys(sheet.sheetData[0] || {}).length;
    const emptyCells = sheet.sheetData.reduce((count, row) => {
      return count + Object.values(row || {}).filter(value =>
        !value || value.toString().trim() === ''
      ).length;
    }, 0);

    const emptyRatio = emptyCells / totalCells;
    return emptyRatio > 0.5; // 如果超过50%的单元格为空，可能有问题
  });

  if (suspiciousSheets.length > 0) {
    console.log('发现可疑的Sheet，可能包含合并单元格:', suspiciousSheets.map(s => s.sheetName));
    ElMessage.warning('检测到文件可能包含合并单元格或格式异常，建议检查文件格式');
  }

  // 2. 检查是否有空的sheet表
  const hasEmptySheets = sheetList.some(sheet => {
    // 检查sheetData是否为空
    if (!sheet.sheetData || sheet.sheetData.length === 0) {
      return true;
    }

    // 检查errorMsg中的空表关键字
    if (sheet.errorMsg && (
      sheet.errorMsg.includes('数据为空') ||
      sheet.errorMsg.includes('为空') ||
      sheet.errorMsg.includes('empty') ||
      sheet.errorMsg.toLowerCase().includes('no data')
    )) {
      return true;
    }

    // 检查是否所有行都是空的
    const allRowsEmpty = sheet.sheetData.every(row => {
      if (!row || typeof row !== 'object') return true;
      const values = Object.values(row);
      return values.every(value => !value || value.toString().trim() === '');
    });

    return allRowsEmpty;
  });

  if (hasEmptySheets) {
    ElMessage.error('存在空的Sheet表，请删除空表或添加数据后再上传');
    return false;
  }

  // 3. 检查sheet表数量是否超过5个
  if (sheetList.length > 5) {
    ElMessage.error('最多支持5个Sheet表，请拆分文件后再上传');
    return false;
  }

  // 4. 检查sheet表名是否重复
  const sheetNames = sheetList.map(sheet => sheet.sheetName);
  const uniqueSheetNames = [...new Set(sheetNames)];
  if (sheetNames.length !== uniqueSheetNames.length) {
    ElMessage.error('Sheet表名重复，请修改表名后再上传');
    return false;
  }

  return true;
};

// 上传文件下一步
const uploadNext = async (formEl) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (fileList.value.length === 0) {
        ElMessage.warning('请先选择要上传的文件');
        return;
      }

      // 获取当前文件
      const currentFile = fileList.value[0];

      // 手动调用 beforeUpload 方法进行校验
      if (!beforeUpload(currentFile.raw)) {
        return;
      }
      // 创建FormData对象并传给后端
      const formData = new FormData();
      formData.append('file', currentFile.raw);
      formData.append('dataName', currentFile.name);

      try {
        // 这里添加实际的文件上传API调用
        // const response = await axios.post('/api/upload', formData);
        // console.log('文件上传成功', fileList.value[0]);

        dataUpload(formData).then(res => {
          console.log(res, '上传成功');
          console.log('API返回的sheetList:', res.result.sheetList);

          // 验证上传的文件
          if (!validateFileUpload(res.result.sheetList)) {
            console.log('文件验证失败，阻止继续上传');
            return; // 验证失败，直接返回
          }

          console.log('文件验证通过，继续处理');

          fileName.value = res.result.fileName
          fileSize.value = res.result.fileSize
          filePath.value = res.result.filePath


          sheetListData.value = res.result.sheetList.map((item) => {
            return {
              id: item.sheetIndex,
              sheetName: item.sheetName,
              checked: item.checked,
              errorMsg: item.errorMsg,
              previewColumns: item.previewColumns.map((column) => {
                return {
                  prop: column.columnProp,
                  selectValue: column.columnTypology,
                  inputValue: column.columnText
                }
              }),
              sheetData: item.sheetData.map((data) => {
                return {
                  ...data
                }
              })
            }
          })
          getSheetList(sheetListData.value)

          console.log('上传文件oooo', sheetListData.value)

          // 获取sheet列表
          stepsActive.value = 1
          // ElMessage.success('文件上传成功')

        }).catch(error => {
          console.error('上传失败:', error);

          // 根据错误信息提供更具体的提示
          let errorMessage = '文件上传失败，请重试';

          if (error && error.message) {
            const errorMsg = error.message.toLowerCase();
            if (errorMsg.includes('合并单元格') || errorMsg.includes('merged')) {
              errorMessage = 'Excel文件包含合并单元格，请处理后再上传';
            } else if (errorMsg.includes('空') || errorMsg.includes('empty')) {
              errorMessage = '文件包含空的Sheet表，请检查文件内容';
            } else if (errorMsg.includes('格式') || errorMsg.includes('format')) {
              errorMessage = '文件格式不正确，请检查文件是否为有效的Excel文件';
            } else if (errorMsg.includes('大小') || errorMsg.includes('size')) {
              errorMessage = '文件大小超出限制，请压缩文件后重试';
            }
          }

          ElMessage.error(errorMessage);
        });
        // 模拟上传成功
        // getSheetList(sheetListData.value);
        // stepsActive.value = 1;

      } catch (error) {
        ElMessage.error('文件上传失败，请重试');
      }
    } else {
      console.log('error submit!', fields);
    }
  });
}
// 格式化接口数据
const getSheetList = (list) => {
  list.forEach((item) => {
    sheetList.value.push({
      ...item,
      previewColumns: item.previewColumns.map((column) => ({
        ...column,
        align: 'center',
        headerSlot: true,
        readonly: true
      }))
    })
  })
}

// --------预览----------
// 修改 previewColumns 计算属性
const previewColumns = computed(() => {
  const activeSheetData = sheetList.value.find((sheetIndex) => sheetIndex.id == acitveSheet.value)
  if (!activeSheetData) return []

  // 只在第一次或切换sheet时更新currentColumns
  if (
    currentColumns.value.length === 0 ||
    currentColumns.value[0]?.sheetId !== activeSheetData.id
  ) {
    // 从sheetList中获取当前sheet的列配置
    currentColumns.value = activeSheetData.previewColumns.map((column) => {
      return {
        ...column,
        sheetId: activeSheetData.id,
        prop: column.prop,
        align: column.align || 'center',
        headerSlot: true,
        selectValue: column.selectValue || '',
        inputValue: column.inputValue || '',
        readonly: column.readonly !== undefined ? column.readonly : true
      }
    })
  }

  return currentColumns.value
})

// Add computed property for sheetData
const sheetData = computed(() => {
  const activeSheetData = sheetList.value.find((sheet) => {
    return sheet.id == acitveSheet.value
  })
  return activeSheetData ? activeSheetData.sheetData : []
})
const acitveSheetClick = (item, event) => {
  // 在切换sheet之前，保存当前sheet的修改到sheetList
  if (currentColumns.value.length > 0) {
    const currentSheetId = currentColumns.value[0]?.sheetId
    const currentSheet = sheetList.value.find((sheet) => sheet.id === currentSheetId)
    if (currentSheet) {
      currentSheet.previewColumns = currentColumns.value.map((column) => ({
        ...column,
        selectValue: column.selectValue,
        inputValue: column.inputValue,
        readonly: column.readonly
      }))
    }
  }

  acitveSheet.value = item.id
  // 清空currentColumns，强制在下次计算时更新
  currentColumns.value = []
  // 增加 tableKey 触发表格刷新
  tableKey.value++
}

const handleSelectChange = (column, value) => {
  const targetColumn = currentColumns.value.find((col) => col.prop === column.prop)
  if (targetColumn) {
    targetColumn.selectValue = value

    // 同步更新到 sheetList
    const currentSheet = sheetList.value.find((sheet) => sheet.id === acitveSheet.value)
    if (currentSheet) {
      const sheetColumn = currentSheet.previewColumns.find((col) => col.prop === column.prop)
      if (sheetColumn) {
        sheetColumn.selectValue = value
      }
    }
  }
}
const toggleEditable = (column) => {
  const targetColumn = currentColumns.value.find((col) => col.prop === column.prop)
  if (targetColumn) {
    targetColumn.readonly = !targetColumn.readonly

    // 同步更新到 sheetList
    const currentSheet = sheetList.value.find((sheet) => sheet.id === acitveSheet.value)
    if (currentSheet) {
      const sheetColumn = currentSheet.previewColumns.find((col) => col.prop === column.prop)
      if (sheetColumn) {
        sheetColumn.readonly = !sheetColumn.readonly
      }
    }
  }
}
const okAndUpload = () => {
  console.log('----确认上传文件',sheetList.value)
  // 转换数据格式
  const transformedData = {
    datasourceType: 1,
    fileName: fileName.value,
    fileSize: fileSize.value,
    filePath: filePath.value, // 这个值需要从上传接口的响应中获取
    datasourceName: fileUploadForm.dataName,
    sheetList: sheetList.value.map(sheet => {
      return {
        sheetIndex: sheet.id,
        sheetName: sheet.sheetName,
        checked: sheet.checked,
        errorMsg: sheet.errorMsg,
        previewColumns: sheet.previewColumns.map((column, index) => {
          return {
            columnIndex: index,
            columnProp: column.prop,
            columnTypology: column.selectValue,
            columnText: column.inputValue
          }
        })
      }
    })
  }

  console.log('转换后的数据格式：', transformedData)
  
  // 调用后端API保存数据
  dataSave(transformedData).then(res => {
    if (res.code === 0) {
      ElMessage.success('保存成功')
      emit('update-success') // 触发更新成功事件，通知父组件刷新列表
      if (stepsActive.value++ > 2) stepsActive.value = 0
      emit('close') // 触发关闭事件
    }
  }).catch(error => {
    ElMessage.error('保存失败：' + error.message)
  })

  if (stepsActive.value++ > 2) stepsActive.value = 0
  emit('close') // 触发关闭事件
}
onMounted(() => {
  //console.log('3.-组件挂载到页面之后执行-------onMounted')
})
// 使用toRefs解构
// let { } = { ...toRefs(data) }
// defineExpose({})
</script>
<style scoped lang="scss">
.fileUpload {
  height: 100%;
  overflow: auto;

  .title-area {
    display: flex;
    align-items: center;
    padding: 0 20px;
    
    .data-source-name {
      font-size: 16px;
      color: #2744d6;
      font-weight: 500;
    }
  }

  :deep(.el-setp) {
    position: relative;
  }

  :deep(.el-step__line) {
    width: 78%;
    margin-left: auto;
    margin-right: 10px;
  }

  :deep(.el-step__main) {
    width: 70px;
    position: absolute;
    top: -6px;
    left: 27px;
  }

  :deep(.is-finish .el-step__icon.is-text) {
    color: #fff;
    background: #2744d6;
    border-color: #2744d6;
  }

  :deep(.el-step__title.is-finish) {
    color: #2744d6;
  }

  :deep(.is-success .el-step__icon.is-text) {
    color: #2744d6;
    border-color: #2744d6;
  }

  :deep(.el-step__title.is-success) {
    color: #2744d6;
  }

  .fileUploadContent {
    max-width: 60%;
    margin: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    :deep(.el-upload-dragger) {
      background: #eff2ff;
    }

    .upload-fileList {
      padding: 8px 12px;
      background-color: #f5f7fa;
      border-radius: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 8px 0;
      transition: all 0.3s;

      &:hover {
        background-color: #eef1f6;
      }

      .upload-fileList-info {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0; // 防止文件名过长导致布局问题

        .upload-fileList-img {
          width: 36px;
          height: 44px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .file-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #606266;
          margin-right: 12px;
        }

        .file-size {
          color: #909399;
          font-size: 13px;
          flex-shrink: 0;
        }
      }

      .file-actions {
        margin-left: 12px;

        .delete-icon {
          cursor: pointer;
          color: #909399;
          transition: color 0.3s;

          &:hover {
            color: #f56c6c;
          }
        }
      }
    }

    .upload-info {
      margin-top: 10px;
      padding: 10px;
      background-color: #f5f7fa;
      border-radius: 4px;
      color: #606266;
    }

    .fileUploadForm {
      width: 100%;
      margin: auto;
      margin-top: 50px;
    }

    ul {
      padding: 0px;

      li {
        margin: 5px 0;
        font-size: 14px;
        color: #333;
      }
    }
  }

  .fileUploadContentSecond {
    margin: auto;
    display: flex;
    flex-direction: column;

    :deep(.el-select) {
      width: auto;
      min-width: 80px !important;
    }

    :deep(.el-select__wrapper) {
      box-shadow: none;
    }

    .sheetoptionsBody {
      width: fit-content;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      margin: auto;
      margin-top: 50px;
      background: #f5f7fa;
      padding: 4px;
      border-radius: 10px;

      .sheetTabs {
        width: 230px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 6px;

        .sheetTabsName {
          color: #999;
          font-size: 14px;
        }
      }
    }

    .sheetisActive {
      width: 100%;
      height: 6px;
      border-radius: 5px;
      margin-bottom: 5px;
    }
  }
}
</style>
