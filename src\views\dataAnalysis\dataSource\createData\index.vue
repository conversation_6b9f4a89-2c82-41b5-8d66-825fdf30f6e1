<template>
  <div class="maskLayer" @click.self="close">
    <div class="outerLayer">
      <div style="height: 40px; line-height: 40px; text-align: center; font-size: 20px; display: flex; justify-content: space-between">
        <span style="color: #2744d6; font-size: 24px; font-style: normal; font-weight: 700; line-height: 24px">新建数据源</span>
        <el-icon @click="close"><svg-icon name="close" /></el-icon>
      </div>
      <div class="content">
        <div class="leftList">
          <el-collapse v-model="activeNames" @change="collapseHandleChange">
            <el-collapse-item title="本地文件" name="1">
              <div :class="item.name == collapseActive ? 'collapseItem isActive' : 'collapseItem'" v-for="item in collapseFileList" :key="item.name" @click="dataBaseClick(item, 'file')">
                <img :src="item.value" style="width: 20px" />
                {{ item.name }}
              </div>
            </el-collapse-item>
            <el-collapse-item title="数据库" name="2">
              <div 
              :class="item.name == collapseActive ? 'collapseItem isActive' : 'collapseItem'"
               v-for="item in dataBaseList" 
               :key="item.name" 
               @click="dataBaseClick(item, 'dataBase')">
                <img :src="item.value" style="width: 20px" />
                {{ item.name }}
              </div>
            </el-collapse-item>
            <el-collapse-item title="API" name="3">
              <div :class="item.name == collapseActive ? 'collapseItem isActive' : 'collapseItem'" v-for="item in collapseApiList" :key="item.name" @click="dataBaseClick(item, 'API')">
                <img :src="item.value" style="width: 20px" />
                {{ item.name }}
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="chiropractic">
          <!-- 未选择 -->
          <div class="noStatusSelected" v-if="initialTip == 'init'">
            <img style="width: 103px; height: 100px" src="@/assets/imgs/datadempement/folder.png" alt="" />
            <p class="universal-font-color">请选择左侧数据源列表查看详情</p>

            <div class="tipText">
              <span class="universal-font-color" style="text-align: center">
                <el-icon style="color: #fa9600"><WarningFilled /></el-icon>
                目前支持6种数据库类型、本地表格文件，
                <br />
                其他类型数据源暂未开放
              </span>
            </div>
          </div>
          <!-- 数据库表单 -->
          <createDataBase ref="dataBaseRef" v-if="initialTip == 'dataBase'" :prop="currentDbType" @update-success="handleUpdateSuccess" />
          <!-- 文件上传 -->
          <createUpload v-if="initialTip == 'file'" @close="close" @update-success="handleUpdateSuccess" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, onMounted} from 'vue';
import {WarningFilled} from '@element-plus/icons-vue';
import SvgIcon from '@/components/SvgIcon.vue';
import createDataBase from './createDataBase.vue';
import createUpload from './createUpload.vue';
import mysql from '@/assets/imgs/datadempement/mysql.png';
import clickHouse from '@/assets/imgs/datadempement/ClickHouse.png';
import gauss from '@/assets/imgs/datadempement/gaoSi.png';
import dermal from '@/assets/imgs/datadempement/dermal.png';
import kingbaseEs from '@/assets/imgs/datadempement/renDajincang.png';
import postgresql from '@/assets/imgs/datadempement/postgresql.png';
import excel from '@/assets/imgs/datadempement/excel.png';
import API from '@/assets/imgs/datadempement/API.png';

const emit = defineEmits(['close', 'update-success']);
const activeNames = ref(['1']); // 默认展开的面板
let initialTip = ref('init');
let currentDbType = ref('');
let dataBaseList = ref([
  {
    name: 'Mysql',
    value: mysql,
    prop: 'Mysql',
  },
  {
    name: 'Click House',
    value: clickHouse,
    prop: 'Click House',
  },
  {
    name: 'GaussDB',
    value: gauss,
    prop: 'GaussDB',
  },
  {
    name: '达梦数据库',
    value: dermal,
    prop: '达梦数据库',
  },
  {
    name: '人大金仓',
    value: kingbaseEs,
    prop: '人大金仓',
  },
  {
    name: 'PostgreSQL',
    value: postgresql,
    prop: 'PostgreSQL',
  },
]);
let collapseFileList = ref([
  {
    name: '本地文件',
    value: excel,
  },
]);
let collapseApiList = ref([
  {
    name: 'API数据',
    value: API,
  },
]);
let collapseActive = ref('');
const dataBaseRef = ref(null);

const dataBaseClick = (item, tips) => {
  initialTip.value = tips;
  collapseActive.value = item.name;
  if (tips === 'dataBase') {
    currentDbType.value = item.prop;
  }
  dataBaseRef.value?.resetFields();
};

onMounted(() => {});

const close = () => {
  emit('close'); // 触发关闭事件
};

const handleUpdateSuccess = () => {
  emit('update-success'); // 触发更新成功事件，通知父组件刷新列表
};

const collapseHandleChange = () => {};

defineExpose({});
</script>
<style scoped lang="scss">
.universal-font-color {
  color: #999999;
}
.maskLayer {
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0px;
  left: 0px;
  z-index: 1003;
  .outerLayer {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1005;
    width: 100vw;
    height: 90vh;
    background: #f5f7fa;
    padding: 20px 15px 10px 15px;
    border-radius: 15px 15px 0 0;

    .content {
      width: 100%;
      height: calc(100% - 40px);
      display: flex;
      flex-direction: row;
      .leftList {
        width: 300px;
        height: 100%;
        padding: 0 10px 0 0;
        .el-collapse {
          width: 100%;
          flex: 1;
          overflow-y: auto;
          border: none;
          scrollbar-gutter: stable; /* 保证滚动条出现时不会改变布局 */
          :deep(.el-collapse-item) {
            border-radius: 10px !important;
            border: 1px solid #eee !important;
            margin-bottom: 10px;
            padding: 10px;
            background-color: #fff;
          }
          :deep(.el-collapse-item__header) {
            background-color: #fff;
            border: none;
          }
          :deep(.el-collapse-item__content) {
            background-color: #fff;
            padding-bottom: 0;
          }
          :deep(.el-collapse-item__wrap) {
            border: none;
            background-color: #fff;
            .collapseItem {
              width: 100%;
              margin: auto;
              padding: 15px;
              margin-top: 10px;
              border-radius: 10px;
              background: #f5f7fa;
              color: #666;
              border: 1px solid #f5f7fa;
              .details-title {
                font-weight: 700;
                font-size: 14px;
              }
              .details {
                display: flex;
                justify-content: space-between;
                flex-wrap: nowrap;
                font-size: 12px;
              }
            }
            .isActive {
              border-color: #c6d0f7;
              background: #e7ecf9;
              color: #2744d6;
            }
          }
        }
        .collapseItem {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          padding: 10px;
          border-radius: 10px;
          background: #fff;
          img {
            margin-right: 10px;
          }
        }
      }
      .chiropractic {
        width: calc(100vw - 330px);
        height: 100%;
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        position: relative;
        border: 1px solid #eeeeee;
        .noStatusSelected {
          width: 60%;
          height: 320px;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          border: 1px dashed #ccc;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%); /* 水平和垂直居中 */
          .tipText {
            padding: 16px;
            display: flex;
            align-items: center;
            background-color: #f5f7fa;
            border-radius: 10px;
          }
        }
        :deep(.el-scrollbar) {
          z-index: 99999;
        }
      }
    }
  }
}
</style>
