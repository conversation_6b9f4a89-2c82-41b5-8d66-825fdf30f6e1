<template>
  <div>
    <span style="font-size: 18px">
      {{ props.editMode ? '编辑' : '' }}数据库{{ props.prop }}
      <span v-if="props.editMode && props.editData?.originalName" style="color: #666; font-size: 14px;">
        ({{ props.editData.originalName }})
      </span>
    </span>
    <!-- MySQL表单 -->
    <div v-if="props.prop === 'Mysql'" class="mySql">
      <el-form ref="mySqlFormRef" style="max-width: 100%; margin-top: 20px" :model="mySqlForm"
        label-width="120px" :rules="mySqlRules" label-position="left">
        <el-form-item label="数据源名称" prop="datasourceName">
          <el-input v-model="mySqlForm.datasourceName" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库地址" prop="databaseIp">
          <el-input v-model="mySqlForm.databaseIp" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口" prop="databasePort">
          <el-input v-model="mySqlForm.databasePort" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库名称" prop="databaseInstance">
          <el-input v-model="mySqlForm.databaseInstance" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="用户名" prop="databaseUsername">
          <el-input v-model="mySqlForm.databaseUsername" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="databasePassword">
          <el-input v-model="mySqlForm.databasePassword" type="password" autocomplete="off" show-password />
        </el-form-item>
        <el-form-item label="数据库版本" prop="databaseVersion">
          <el-select v-model="mySqlForm.databaseVersion" style="width: 100%" clearable>
            <el-option v-for="item in versionList" :key="item.value" :label="item.value" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <div style="max-width: 60%; margin: auto; color: #999; font-size: 14px">提示：支持5.5、5.6、5.7、8.0版本</div>
    </div>
    <!-- ClickHouse表单 -->
    <div v-else-if="props.prop === 'Click House'" class="clickHouse">
      <el-form ref="clickHouseFormRef" style="max-width: 100%; margin-top: 20px" :model="clickHouseForm"
        label-width="120px" :rules="clickHouseRules" label-position="left">
        <el-form-item label="数据源名称" prop="datasourceName">
          <el-input v-model="clickHouseForm.datasourceName" placeholder="数据源配置列表显示名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库地址" prop="databaseIp">
          <el-input v-model="clickHouseForm.databaseIp" placeholder="数据库地址" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口" prop="databasePort">
          <el-input v-model="clickHouseForm.databasePort" placeholder="8123" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库名称" prop="databaseInstance">
          <el-input v-model="clickHouseForm.databaseInstance" placeholder="数据库名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="用户名" prop="databaseUsername">
          <el-input v-model="clickHouseForm.databaseUsername" placeholder="请输入用户名" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="databasePassword">
          <el-input v-model="clickHouseForm.databasePassword" placeholder="请输入密码" type="password" autocomplete="off" show-password />
        </el-form-item>
        <el-form-item label="数据库版本" prop="databaseVersion">
          <el-select v-model="clickHouseForm.databaseVersion" style="width: 100%" clearable>
            <el-option v-for="item in clickHouseVersionList" :key="item.value" :label="item.value" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!-- GaussDB表单 -->
    <div v-else-if="props.prop === 'GaussDB'" class="gaussDb">
      <el-form ref="gaussDbFormRef" style="max-width: 100%; margin-top: 20px" :model="gaussDbForm"
        label-width="120px" :rules="gaussDbRules" label-position="left">
        <el-form-item label="数据源名称" prop="datasourceName">
          <el-input v-model="gaussDbForm.datasourceName" placeholder="数据源配置列表显示名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库地址" prop="databaseIp">
          <el-input v-model="gaussDbForm.databaseIp" placeholder="数据库地址" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口" prop="databasePort">
          <el-input v-model="gaussDbForm.databasePort" placeholder="8000" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库名称" prop="databaseInstance">
          <el-input v-model="gaussDbForm.databaseInstance" placeholder="数据库名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Schema">
          <el-input v-model="gaussDbForm.databaseSchema" placeholder="请输入schema" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="用户名" prop="databaseUsername">
          <el-input v-model="gaussDbForm.databaseUsername" placeholder="请输入用户名" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="databasePassword">
          <el-input v-model="gaussDbForm.databasePassword" placeholder="请输入密码" type="password" autocomplete="off" show-password />
        </el-form-item>
        <el-form-item label="数据库版本" prop="databaseVersion">
          <el-select v-model="gaussDbForm.databaseVersion" style="width: 100%" clearable>
            <el-option v-for="item in gaussDbVersionList" :key="item.value" :label="item.value" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!-- 达梦数据库表单 -->
    <div v-else-if="props.prop === '达梦数据库'" class="damengDb">
      <el-form ref="damengDbFormRef" style="max-width: 100%; margin-top: 20px" :model="damengDbForm"
        label-width="120px" :rules="damengDbRules" label-position="left">
        <div style="max-width: 100%; margin-bottom: 20px; color: #999; font-size: 14px">
          <el-icon><InfoFilled /></el-icon> 提示：支持7.0、8.0版本
        </div>
        <el-form-item label="显示名称" prop="datasourceName">
          <el-input v-model="damengDbForm.datasourceName" placeholder="数据源配置列表显示名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库地址" prop="databaseIp">
          <el-input v-model="damengDbForm.databaseIp" placeholder="数据库地址" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口" prop="databasePort">
          <el-input v-model="damengDbForm.databasePort" placeholder="5236" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库" prop="databaseInstance">
          <el-input v-model="damengDbForm.databaseInstance" placeholder="数据库名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Schema">
          <el-input v-model="damengDbForm.databaseSchema" placeholder="请输入schema" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="用户名" prop="databaseUsername">
          <el-input v-model="damengDbForm.databaseUsername" placeholder="请输入用户名" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="databasePassword">
          <el-input v-model="damengDbForm.databasePassword" placeholder="请输入密码" type="password" autocomplete="off" show-password />
        </el-form-item>
      </el-form>
    </div>
    <!-- 人大金仓表单 -->
    <div v-else-if="props.prop === '人大金仓'" class="kingbaseDb">
      <el-form ref="kingbaseDbFormRef" style="max-width: 100%; margin-top: 20px" :model="kingbaseDbForm"
        label-width="120px" :rules="kingbaseDbRules" label-position="left">
        <div style="max-width: 100%; margin-bottom: 20px; color: #999; font-size: 14px">
          <el-icon><InfoFilled /></el-icon> 提示：支持7.1.2版本
        </div>
        <el-form-item label="显示名称" prop="datasourceName">
          <el-input v-model="kingbaseDbForm.datasourceName" placeholder="数据源配置列表显示名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库地址" prop="databaseIp">
          <el-input v-model="kingbaseDbForm.databaseIp" placeholder="数据库地址" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口" prop="databasePort">
          <el-input v-model="kingbaseDbForm.databasePort" placeholder="5432" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库" prop="databaseInstance">
          <el-input v-model="kingbaseDbForm.databaseInstance" placeholder="数据库名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Schema">
          <el-input v-model="kingbaseDbForm.databaseSchema" placeholder="PUBLIC" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="用户名" prop="databaseUsername">
          <el-input v-model="kingbaseDbForm.databaseUsername" placeholder="请输入用户名" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="databasePassword">
          <el-input v-model="kingbaseDbForm.databasePassword" placeholder="请输入密码" type="password" autocomplete="off" show-password />
        </el-form-item>
        <el-form-item label="数据库版本" prop="databaseVersion">
          <el-select v-model="kingbaseDbForm.databaseVersion" placeholder="请选择数据库版本" style="width: 100%" clearable>
            <el-option v-for="item in kingbaseVersionList" :key="item.value" :label="item.value" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!-- PostgreSQL表单 -->
    <div v-else-if="props.prop === 'PostgreSQL'" class="postgresqlDb">
      <el-form ref="postgresqlDbFormRef" style="max-width: 100%; margin-top: 20px" :model="postgresqlDbForm"
        label-width="120px" :rules="postgresqlDbRules" label-position="left">
        <div style="max-width: 100%; margin-bottom: 20px; color: #999; font-size: 14px">
          <el-icon><InfoFilled /></el-icon> 提示：支持PostgreSQL 8.2及以上版本
        </div>
        <el-form-item label="显示名称" prop="datasourceName">
          <el-input v-model="postgresqlDbForm.datasourceName" placeholder="数据源配置列表显示名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库地址" prop="databaseIp">
          <el-input v-model="postgresqlDbForm.databaseIp" placeholder="数据库地址" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="端口" prop="databasePort">
          <el-input v-model="postgresqlDbForm.databasePort" placeholder="5432" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="数据库" prop="databaseInstance">
          <el-input v-model="postgresqlDbForm.databaseInstance" placeholder="数据库名称" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="Schema">
          <el-input v-model="postgresqlDbForm.databaseSchema" placeholder="public" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="用户名" prop="databaseUsername">
          <el-input v-model="postgresqlDbForm.databaseUsername" placeholder="请输入用户名" type="text" autocomplete="off" />
        </el-form-item>
        <el-form-item label="密码" prop="databasePassword">
          <el-input v-model="postgresqlDbForm.databasePassword" placeholder="请输入密码" type="password" autocomplete="off" show-password />
        </el-form-item>
      </el-form>
    </div>
    <!-- 底部按钮区域，所有数据库类型通用 -->
    <div style="position: absolute; bottom: 20px; right: 20px; display: flex; justify-content: flex-end">
      <el-button @click="linkTesting(currentFormRef)">连接测试</el-button>
      <el-button v-if="!props.editMode" type="primary" @click="submitForm(currentFormRef)" :disabled="!isTestSuccess">确定</el-button>
      <el-button v-else type="primary" @click="submitEditForm(currentFormRef)" :disabled="!isTestSuccess">更新</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, defineProps, computed, watch, onMounted } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { testDatabaseConnection, saveDatabase, updateDatabase } from '@/api/datainfor/dataupload';
import {ElMessage} from 'element-plus';


const emit = defineEmits(['close', 'update-success']);
const props = defineProps({
  prop: {
    type: String,
    required: true,
    default: '',
  },
  editMode: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: null,
  },
});

// MySQL表单
const mySqlFormRef = ref(null);
const mySqlForm = reactive({
  datasourceName: '',
  databaseType: 2,
  databaseIp: '',
  databasePort: '',
  databaseInstance: '',
  databaseSchema: '',
  databaseUsername: '',
  databasePassword: '',
  databaseVersion: '',
});
const mySqlRules = reactive({
  datasourceName: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }],
  databaseIp: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  databasePort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  databaseInstance: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  databaseUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  databasePassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  databaseVersion: [{ required: true, message: '请选择数据库版本', trigger: 'change' }],
});
const versionList = ref([{ value: '5.5' }, { value: '5.6' }, { value: '5.7' }, { value: '8.0' }]);
const gaussDbVersionList = ref([{ value: '3.1.0' }]);

// 达梦数据库表单
const damengDbFormRef = ref(null);
const damengDbForm = reactive({
  datasourceName: '',
  databaseType: 7, // 假设达梦数据库类型为7
  databaseIp: '',
  databasePort: '5236',
  databaseInstance: '',
  databaseSchema: '',
  databaseUsername: '',
  databasePassword: '',
  databaseVersion: '8.0',
});
const damengDbRules = reactive({
  datasourceName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
  databaseIp: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  databasePort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  databaseInstance: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  databaseUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  databasePassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});

// 人大金仓表单
const kingbaseDbFormRef = ref(null);
const kingbaseDbForm = reactive({
  datasourceName: '',
  databaseType: 6 , // 假设人大金仓数据库类型为6
  databaseIp: '',
  databasePort: '5432',
  databaseInstance: '',
  databaseSchema: 'PUBLIC',
  databaseUsername: '',
  databasePassword: '',
  databaseVersion: '7.1.2',
});
const kingbaseDbRules = reactive({
  datasourceName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
  databaseIp: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  databasePort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  databaseInstance: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  databaseUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  databasePassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  databaseVersion: [{ required: true, message: '请选择数据库版本', trigger: 'change' }],
});
const kingbaseVersionList = ref([{ value: '7.1.2' }]);

// PostgreSQL表单
const postgresqlDbFormRef = ref(null);
const postgresqlDbForm = reactive({
  datasourceName: '',
  databaseType: 3, // PostgreSQL数据库类型为3
  databaseIp: '',
  databasePort: '5432',
  databaseInstance: '',
  databaseSchema: 'public',
  databaseUsername: '',
  databasePassword: '',
  databaseVersion: '8.2',
});
const postgresqlDbRules = reactive({
  datasourceName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
  databaseIp: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  databasePort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  databaseInstance: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  databaseUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  databasePassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});

// ClickHouse表单
const clickHouseFormRef = ref(null);
const clickHouseForm = reactive({
  datasourceName: '',
  databaseType: 5,
  databaseIp: '',
  databasePort: '8123',
  databaseInstance: '',
  databaseSchema: '',
  databaseUsername: '',
  databasePassword: '',
  databaseVersion: '3.1.0',
});
const clickHouseRules = reactive({
  datasourceName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
  databaseIp: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  databasePort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  databaseInstance: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  databaseUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  databasePassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  databaseTimezone: [{ required: true, message: '请选择时区', trigger: 'change' }],
  databaseVersion: [{ required: true, message: '请选择数据库版本', trigger: 'change' }],
});

const clickHouseVersionList = ref([
  { value: '22.8' },
  { value: '21.8' },
  { value: '20.3' },
]);

// GaussDB表单
const gaussDbFormRef = ref(null);
const gaussDbForm = reactive({
  datasourceName: '',
  databaseType: 8,
  databaseIp: '',
  databasePort: '5432',
  databaseInstance: '',
  databaseSchema: '',
  databaseUsername: '',
  databasePassword: '',
  databaseVersion: '3.1.0',
});
const gaussDbRules = reactive({
  datasourceName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
  databaseIp: [{ required: true, message: '请输入数据库地址', trigger: 'blur' }],
  databasePort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
  databaseInstance: [{ required: true, message: '请输入数据库名称', trigger: 'blur' }],
  databaseSchema: [{ required: true, message: '请输入Schema', trigger: 'blur' }],
  databaseUsername: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
  databasePassword: [{ required: true, message: '请输入密码', trigger: 'blur' }],
});

// 当前表单ref
const currentFormRef = computed(() => {
  if (props.prop === 'Mysql') return mySqlFormRef.value;
  if (props.prop === 'Click House') return clickHouseFormRef.value;
  if (props.prop === 'GaussDB') return gaussDbFormRef.value;
  if (props.prop === '达梦数据库') return damengDbFormRef.value;
  if (props.prop === '人大金仓') return kingbaseDbFormRef.value;
  if (props.prop === 'PostgreSQL') return postgresqlDbFormRef.value;
  return null;
});

const isTestSuccess = ref(false);

const linkTesting = async formEl => {
  if (!formEl) return;
  let formModel;
  if (props.prop === 'Mysql') formModel = mySqlForm;
  else if (props.prop === 'Click House') formModel = clickHouseForm;
  else if (props.prop === 'GaussDB') formModel = gaussDbForm;
  else if (props.prop === '达梦数据库') formModel = damengDbForm;
  else if (props.prop === '人大金仓') formModel = kingbaseDbForm;
  else if (props.prop === 'PostgreSQL') formModel = postgresqlDbForm;
  else return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      testDatabaseConnection(formModel).then(res => {
        console.log(res, '测试结果');
        
        if (res) {
          ElMessage({ message: '数据库链接测试成功', type: 'success' });
          isTestSuccess.value = true;
        } else {
          ElMessage({ message: '数据库链接测试失败', type: 'error' });
        }
      }).catch(error => {
        ElMessage({ message: '测试过程中发生错误：' + error.message, type: 'error' });
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

const submitForm = async formEl => {
  if (!formEl) return;
  let formModel;
  if (props.prop === 'Mysql') formModel = mySqlForm;
  else if (props.prop === 'Click House') formModel = clickHouseForm;
  else if (props.prop === 'GaussDB') formModel = gaussDbForm;
  else if (props.prop === '达梦数据库') formModel = damengDbForm;
  else if (props.prop === '人大金仓') formModel = kingbaseDbForm;
  else if (props.prop === 'PostgreSQL') formModel = postgresqlDbForm;
  else return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      saveDatabase(formModel).then(res => {
        if (res) {
          ElMessage({ message: '保存成功', type: 'success' });
          emit('update-success'); // 触发更新成功事件，通知父组件刷新列表
          emit('close');
          formEl.resetFields();
        } else {
          ElMessage({ message: '保存失败', type: 'error' });
        }
      }).catch(error => {
        console.log(error, '保存过程中发生错误');
        ElMessage({ message: '保存过程中发生错误：' + error.message, type: 'error' });
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

// 编辑模式下的提交函数
const submitEditForm = async formEl => {
  if (!formEl) return;
  let formModel;
  if (props.prop === 'Mysql') formModel = mySqlForm;
  else if (props.prop === 'Click House') formModel = clickHouseForm;
  else if (props.prop === 'GaussDB') formModel = gaussDbForm;
  else if (props.prop === '达梦数据库') formModel = damengDbForm;
  else if (props.prop === '人大金仓') formModel = kingbaseDbForm;
  else if (props.prop === 'PostgreSQL') formModel = postgresqlDbForm;
  else return;

  await formEl.validate((valid, fields) => {
    if (valid) {
      // 添加 id 字段用于更新
      const updateData = { ...formModel, id: props.editData.id };
      updateDatabase(updateData).then(res => {
        if (res) {
          ElMessage({ message: '更新成功', type: 'success' });
          emit('update-success');
        } else {
          ElMessage({ message: '更新失败', type: 'error' });
        }
      }).catch(error => {
        ElMessage({ message: '更新过程中发生错误：' + error.message, type: 'error' });
      });
    } else {
      console.log('error submit!', fields);
    }
  });
};

// 填充编辑数据到表单
const fillEditData = () => {
  if (!props.editData || !props.editMode) return;

  console.log('填充编辑数据:', props.editData);
  const data = props.editData;

  // 为了演示，如果后端数据不完整，我们提供一些示例数据
  const getDefaultData = (dbType) => {
    const defaults = {
      'Mysql': { port: '3306', version: '8.0' },
      'Click House': { port: '8123', version: '21.8' },
      'GaussDB': { port: '5432', version: '3.1.0' },
      '达梦数据库': { port: '5236', version: '8.0' },
      '人大金仓': { port: '5432', version: '7.1.2', schema: 'PUBLIC' },
      'PostgreSQL': { port: '5432', version: '8.2', schema: 'public' }
    };
    return defaults[dbType] || {};
  };

  const defaultData = getDefaultData(props.prop);

  if (props.prop === 'Mysql') {
    Object.assign(mySqlForm, {
      datasourceName: data.datasourceName || data.name || '',
      databaseIp: data.databaseIp || '',
      databasePort: data.databasePort || defaultData.port,
      databaseInstance: data.databaseInstance || '',
      databaseSchema: data.databaseSchema || '',
      databaseUsername: data.databaseUsername || '',
      databasePassword: data.databasePassword || '', // 密码留空，需要用户重新输入
      databaseVersion: data.databaseVersion || defaultData.version,
      databaseType: 2, // MYSQL 正确为 2
    });
  } else if (props.prop === 'Click House') {
    Object.assign(clickHouseForm, {
      datasourceName: data.datasourceName || data.name || '',
      databaseIp: data.databaseIp || '',
      databasePort: data.databasePort || defaultData.port,
      databaseInstance: data.databaseInstance || '',
      databaseSchema: data.databaseSchema || '',
      databaseUsername: data.databaseUsername || '',
      databasePassword: data.databasePassword || '',
      databaseVersion: data.databaseVersion || defaultData.version,
      databaseType: 5, // CLICKHOUSE 正确为 5
    });
  } else if (props.prop === 'GaussDB') {
    Object.assign(gaussDbForm, {
      datasourceName: data.datasourceName || data.name || '',
      databaseIp: data.databaseIp || '',
      databasePort: data.databasePort || defaultData.port,
      databaseInstance: data.databaseInstance || '',
      databaseSchema: data.databaseSchema || '',
      databaseUsername: data.databaseUsername || '',
      databasePassword: data.databasePassword || '',
      databaseVersion: data.databaseVersion || defaultData.version,
      databaseType: 8, // GAUSS 正确为 8
    });
  } else if (props.prop === '达梦数据库') {
    Object.assign(damengDbForm, {
      datasourceName: data.datasourceName || data.name || '',
      databaseIp: data.databaseIp || '',
      databasePort: data.databasePort || defaultData.port,
      databaseInstance: data.databaseInstance || '',
      databaseSchema: data.databaseSchema || '',
      databaseUsername: data.databaseUsername || '',
      databasePassword: data.databasePassword || '',
      databaseVersion: data.databaseVersion || defaultData.version,
      databaseType: 7, // DM 达梦 正确为 7
    });
  } else if (props.prop === '人大金仓') {
    Object.assign(kingbaseDbForm, {
      datasourceName: data.datasourceName || data.name || '',
      databaseIp: data.databaseIp || '',
      databasePort: data.databasePort || defaultData.port,
      databaseInstance: data.databaseInstance || '',
      databaseSchema: data.databaseSchema || defaultData.schema,
      databaseUsername: data.databaseUsername || '',
      databasePassword: data.databasePassword || '',
      databaseVersion: data.databaseVersion || defaultData.version,
      databaseType: 6, // KINGBASE 正确为 6
    });
  } else if (props.prop === 'PostgreSQL') {
    Object.assign(postgresqlDbForm, {
      datasourceName: data.datasourceName || data.name || '',
      databaseIp: data.databaseIp || '',
      databasePort: data.databasePort || defaultData.port,
      databaseInstance: data.databaseInstance || '',
      databaseSchema: data.databaseSchema || defaultData.schema,
      databaseUsername: data.databaseUsername || '',
      databasePassword: data.databasePassword || '',
      databaseVersion: data.databaseVersion || defaultData.version,
      databaseType: 3, // POSTGRESQL 正确为 3
    });
  }

  console.log('填充后的表单数据:', props.prop, getCurrentFormData());
};

// 获取当前表单数据的辅助函数
const getCurrentFormData = () => {
  if (props.prop === 'Mysql') return mySqlForm;
  else if (props.prop === 'Click House') return clickHouseForm;
  else if (props.prop === 'GaussDB') return gaussDbForm;
  else if (props.prop === '达梦数据库') return damengDbForm;
  else if (props.prop === '人大金仓') return kingbaseDbForm;
  else if (props.prop === 'PostgreSQL') return postgresqlDbForm;
  return null;
};

// 监听编辑数据变化
watch(() => props.editData, (newData) => {
  console.log('监听到 editData 变化:', newData);
  console.log('当前 editMode:', props.editMode);
  if (props.editMode && props.editData) {
    fillEditData();
  }
}, { immediate: true });

// 监听 props 变化
watch(() => props.editMode, (newMode) => {
  console.log('editMode 变化:', newMode);
}, { immediate: true });

watch(() => props.prop, (newProp) => {
  console.log('prop 变化:', newProp);
}, { immediate: true });

const resetFields = () => {
  if (props.prop === 'Mysql') mySqlFormRef.value.resetFields();
  else if (props.prop === 'Click House') clickHouseFormRef.value.resetFields();
  else if (props.prop === 'GaussDB') gaussDbFormRef.value.resetFields();
  else if (props.prop === '达梦数据库') damengDbFormRef.value.resetFields();
  else if (props.prop === '人大金仓') kingbaseDbFormRef.value.resetFields();
  else if (props.prop === 'PostgreSQL') postgresqlDbFormRef.value.resetFields();
};
defineExpose({ resetFields });
</script>
<style scoped lang="scss"></style>
