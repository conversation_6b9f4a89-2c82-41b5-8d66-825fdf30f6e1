<template>
  <div class="app-container">
    <div class="datasource-top-row">
      <div class="datasource-header">数据源</div>
      <el-button type="primary" :icon="Plus" @click="createANewDataSource">新建数据源</el-button>
    </div>
    <div class="datasource-main-content">
      <div class="left-content">
        <el-input v-model="search" style="width: 100%" :suffix-icon="Search" placeholder="搜索..." />
        <el-collapse v-model="activeNames" @change="collapseHandleChange" style="width: 100%; height: 100%">
          <!-- MySQL 数据源折叠项 -->
          <el-collapse-item name="1">
            <template #title>
              <img src="@/assets/imgs/datadempement/dataDB.png" class="source-icon" />
              数据库
              <span class="universal-font-color">（{{ mysqlList.length }}）</span>
            </template>
            <div v-for="item in mysqlList" :key="item.id"
              :class="item.id === collapseActive ? 'collapseItem  isActive' : ' collapseItem '"
              @click="selectDataItem(item)" @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false">
              <div class="details-title">{{ item.name }}</div>
              <div class="details">
                <span>{{ item.creator || '张大千' }}</span>
                <span>{{ formatDate(item.createTime) || '2025.02.03 18:20:23' }}</span>
              </div>
              <!-- 操作按钮 -->
              <div v-show="item.showActions" class="action-buttons" @click.stop>
                <el-icon class="action-icon edit-icon" @click="editDataSource(item)">
                  <Edit />
                </el-icon>
                <el-icon class="action-icon delete-icon" @click="confirmDeleteDataSource(item)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </el-collapse-item>
          <!-- 本地文件数据源折叠项 -->
          <el-collapse-item name="2">
            <template #title>
              <img src="@/assets/imgs/datadempement/excel.png" class="source-icon" />
              本地文件
              <span class="universal-font-color">（{{ fileList.length }}）</span>
            </template>
            <div v-for="item in fileList" :key="item.id"
              :class="item.id === collapseActive ? 'collapseItem  isActive' : 'collapseItem '"
              @click="selectDataItem(item)" @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false">
              <div class="details-title">{{ item.name }}</div>
              <div class="details">
                <span>{{ item.creator || '张大千' }}</span>
                <span>{{ formatDate(item.createTime) || '2025.02.03 18:20:23' }}</span>
              </div>
              <!-- 操作按钮 -->
              <div v-show="item.showActions" class="action-buttons" @click.stop>
                <!-- <el-icon class="action-icon edit-icon" @click="editDataSource(item)">
                  <Edit />
                </el-icon> -->
                <el-icon class="action-icon delete-icon" @click="confirmDeleteDataSource(item)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </el-collapse-item>
          <!-- API 数据源折叠项 -->
          <el-collapse-item name="3">
            <template #title>
              <img src="@/assets/imgs/datadempement/API.png" class="source-icon" />
              API
              <span class="universal-font-color">（{{ apiList.length }}）</span>
            </template>
            <div v-for="item in apiList" :key="item.id"
              :class="item.id === collapseActive ? 'collapseItem  isActive' : 'collapseItem '"
              @click="selectDataItem(item)" @mouseenter="item.showActions = true"
              @mouseleave="item.showActions = false">
              <div class="details-title">{{ item.name }}</div>
              <div class="details">
                <span>{{ item.creator || '张大千' }}</span>
                <span>{{ formatDate(item.createTime) || '2025.02.03 18:20:23' }}</span>
              </div>
              <!-- 操作按钮 -->
              <div v-show="item.showActions" class="action-buttons" @click.stop>
                <el-icon class="action-icon edit-icon" @click="editDataSource(item)">
                  <Edit />
                </el-icon>
                <el-icon class="action-icon delete-icon" @click="confirmDeleteDataSource(item)">
                  <Delete />
                </el-icon>
              </div>
            </div>
          </el-collapse-item>


        </el-collapse>
      </div>
      <div class="right-content">
        <el-card v-if="!initialTip" class="newlyBuilt" shadow="never"
          body-style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
          <img style="width: 103px; height: 100px" src="@/assets/imgs/datadempement/folder.png" alt="" />
          <p class="universal-font-color" style="margin: 16px 0 8px 0;">请选择左侧数据源列表查看详情</p>
          <el-alert type="warning" show-icon :closable="false"
            style="background: #f5f7fa; border-radius: 10px; margin-bottom: 0; width: auto;">
            <template #title>
              如没有所需要的数据源，可以点击
              <span class="creat-new-data" @click="createANewDataSource"
                style="color: #3b7bdf; cursor: pointer;">新建数据源</span>
            </template>
          </el-alert>
        </el-card>
        <el-card v-else style="width: 100%; box-shadow: none; border: none;">
          <el-row class="table-header" align="top" justify="space-between" style="margin-bottom: 10px;">
            <el-col :span="12">
              <div class="table-header-title">
                <p>{{ dataSourceDetail.name }}({{ dataSourceDetail.type }})</p>
                <p>
                  <span style="margin-right: 20px">所有者：{{ dataSourceDetail.creator }}</span>
                  <span>创建时间：{{ formatDate(dataSourceDetail.createTime) }}</span>
                </p>
              </div>
            </el-col>
            <el-col :span="6" style="display: flex; align-items: flex-start;">
              <el-input v-model="tableSearch" style="width: 90%;" :suffix-icon="Search" placeholder="搜索表名..." />
            </el-col>
            <el-col :span="6" style="display: flex; align-items: flex-start; justify-content: flex-end;">
              <el-button type="primary" @click="permissionClick">
                <el-icon style="margin-right: 6px"><svg-icon name="permission" /></el-icon>
                权限分配
              </el-button>
            </el-col>
          </el-row>
          <GeneralTables :columns="mainColumns" :data="tableData" :border="true" :pagination="false"
            style="margin-top: 10px">
            <template #column-bb="scope">
              <el-tag :type="getTagType(scope.row.dataStatus)">{{ scope.row.dataStatus }}</el-tag>
            </template>
            <template #column-operation="scope">
              <el-button link type="primary" @click="previewTheData(scope.row)">预览数据</el-button>
              <el-button link type="primary" @click="createDataSet(scope.row)">创建数据集</el-button>
            </template>
          </GeneralTables>
          <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
            <el-pagination v-if="total > 0" :current-page="currentPage" :page-size="pageSize" :total="total"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
              @current-change="handleCurrentChange" @size-change="handleSizeChange" />
          </div>
        </el-card>
      </div>

      <el-dialog v-model="dialogVisible" :title="dialogTitle" width="80%" :show-close="false">
        <template #header="{ titleId, titleClass }">
          <div class="my-header">
            <span :id="titleId" :class="titleClass">{{ dialogTitle }}</span>
            <el-icon style="width: 18px; height: 18px" @click="dialogVisible = false"><svg-icon
                name="close" /></el-icon>
          </div>
        </template>

        <!-- Tab切换 -->
        <el-tabs v-model="activeTab" @tab-click="handleTabClick">
          <el-tab-pane label="数据预览" name="data">
            <GeneralTables :columns="dataPreviewColumns" :data="dataPreviewTableData" :border="true" :stripe="true"
              :pagination="false" style="margin-top: 10px" />
          </el-tab-pane>
          <el-tab-pane label="字段详情" name="field">
            <GeneralTables :columns="previewColumns" :data="previewTableData" :border="true" :stripe="true"
              :pagination="false" style="margin-top: 10px" />
          </el-tab-pane>

        </el-tabs>

        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
      <!-- 添加动画 -->
      <transition name="slide">
        <createDataSource v-if="isShowcreateDataSet" @close="closeCreateDataSet" />
      </transition>
      <permissionAssignment ref="permissionRef" />

      <!-- 编辑数据源对话框 -->
      <el-dialog v-model="editDialogVisible" title="编辑数据源" width="400px" :show-close="false">
        <template #header="{ titleId, titleClass }">
          <div class="my-header">
            <span :id="titleId" :class="titleClass">编辑数据源</span>
            <el-icon style="width: 18px; height: 18px" @click="editDialogVisible = false">
              <svg-icon name="close" />
            </el-icon>
          </div>
        </template>
        <el-form ref="editFormRef" :model="editForm" :rules="editRules" label-width="80px">
          <el-form-item label="数据源名称" prop="name">
            <el-input v-model="editForm.name" placeholder="请输入数据源名称" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="editDialogVisible = false">取消</el-button>
            <el-button type="primary" @click="handleUpdateDataSource">确定</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 数据库编辑抽屉 -->
      <el-drawer v-model="editDatabaseDialogVisible" direction="btt" size="90%" :show-close="false"
        :close-on-click-modal="true" :close-on-press-escape="false" @closed="clearEditDatabaseCache">
        <template #header>
          <div class="drawer-header">
            <span class="drawer-title">
              编辑数据库 - {{ editDatabaseData?.originalName || editDatabaseData?.name || '未知数据源' }}
            </span>
            <el-button class="drawer-close" size="medium" @click="closeEditDatabaseDrawer">
              <el-icon>
                <Close />
              </el-icon>
              关闭
            </el-button>
          </div>
        </template>
        <div class="database-edit-content">
          <div class="leftList">
            <el-collapse v-model="editCollapseActive" disabled>
              <el-collapse-item title="数据库" name="2">
                <div class="collapseItem isActive disabled">
                  <img :src="getDatabaseIcon(editDatabaseType)" style="width: 20px" />
                  {{ editDatabaseType }}
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div class="chiropractic">
            <createDataBase ref="editDatabaseRef"
              v-if="editDatabaseDialogVisible && editDatabaseType && editDatabaseData" :prop="editDatabaseType"
              :edit-mode="true" :edit-data="editDatabaseData" @close="closeEditDatabaseDrawer"
              @update-success="handleDatabaseUpdateSuccess" />
          </div>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from 'vue';
import { Search, Plus, Edit, Delete ,Close} from '@element-plus/icons-vue';
import SvgIcon from '@/components/SvgIcon.vue';
import createDataSource from '@/views/dataAnalysis/dataSource/createData/index.vue';
import createDataBase from '@/views/dataAnalysis/dataSource/createData/createDataBase.vue';
import permissionAssignment from '@/views/dataAnalysis/dataSource/permissionAssignment.vue';
import GeneralTables from '@/components/GeneralTables/index.vue';
import { getDataSourceList, getDataSourceWithTables, getFieldDetail, getDataDetail, updateDataSource, deleteDataSource, getDatabaseConfig } from '@/api/datainfor/dataupload';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
let activeNames = ref(['1']);
let search = ref('');
let tableSearch = ref(''); // 右侧表格搜索
let mysqlList = ref([{ id: '1' }, { id: '2' }]);
let apiList = ref([{ id: '3' }, { id: '4' }]);
let fileList = ref([{ id: '5' }, { id: '6' }, { id: '7' }, { id: '8' }]);
let collapseActive = ref('');
let tableData = ref([]);
let dialogVisible = ref(false);
let dialogTitle = ref('');
let isShowcreateDataSet = ref(false);
let permissionRef = ref(null);
let initialTip = ref(false);
let previewTableData = ref([{ aa: 'XXXXXX' }]);
let dataSourceDetail = ref({
  dataTableList: [],
  creator: '',
  createTime: '',
  name: '',
  type: '',
  id: '',
});
let pageSize = ref(10);
let currentPage = ref(1);
let total = ref(0);
let currentDataSourceId = ref('');
let router = useRouter();

// 搜索防抖定时器
let searchTimer = null;
let tableSearchTimer = null;

// 编辑数据源相关变量
let editDialogVisible = ref(false);
let editFormRef = ref(null);
let editForm = reactive({
  id: '',
  name: ''
});
let editRules = reactive({
  name: [{ required: true, message: '请输入数据源名称', trigger: 'blur' }]
});

// 数据库编辑相关变量
let editDatabaseDialogVisible = ref(false);
let editDatabaseData = ref(null);
let editDatabaseType = ref('');
let editCollapseActive = ref(['2']);
let editDatabaseRef = ref(null);

// 弹窗tab相关变量
let activeTab = ref('field');
let dataPreviewTableData = ref([]);
let dataPreviewColumns = ref([]);
let currentTableInfo = ref({
  databaseId: '',
  tableName: '',
  dataSourceId: null,
  id: ''
});

onMounted(() => {
  getDataList();
});

// 监听搜索框变化
watch(search, () => {
  searchDataSources();
});

watch(tableSearch, () => {
  searchTableData();
});

// 搜索数据源（调用API，带防抖）
const searchDataSources = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  // 设置新的定时器，500ms后执行搜索
  searchTimer = setTimeout(() => {
    const keyword = search.value.trim();
    getDataList(keyword);
  }, 500);
};

// 搜索表格数据（调用API，带防抖）
const searchTableData = () => {
  // 清除之前的定时器
  if (tableSearchTimer) {
    clearTimeout(tableSearchTimer);
  }

  // 设置新的定时器，500ms后执行搜索
  tableSearchTimer = setTimeout(() => {
    if (currentDataSourceId.value) {
      currentPage.value = 1; // 搜索时重置页码
      fetchTableData(currentDataSourceId.value, 1, pageSize.value);
    }
  }, 500);
};



const formatDate = timestamp => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
};


const getDataList = async (searchKeyword = '') => {
  const params = searchKeyword ? { dataSourceName: searchKeyword } : {};
  const res = await getDataSourceList(params);
  console.log("获取数据源列表555555", res);
  if (res) {
    // 为每个数据源项添加showActions属性
    apiList.value = (res.API || []).map(item => ({ ...item, showActions: false }));
    mysqlList.value = (res.DataBase || []).map(item => ({ ...item, showActions: false }));
    fileList.value = (res.Excel || []).map(item => ({ ...item, showActions: false }));
  }
}


const selectDataItem = item => {
  initialTip.value = true;
  currentDataSourceId.value = item.id;
  currentPage.value = 1;
  fetchTableData(item.id, 1, pageSize.value);
};
const closeDialog = () => {
  dialogVisible.value = false;
};
const previewTheData = async row => {
  console.log('预览数据参数:', row);

  try {
    // 保存当前表信息
    currentTableInfo.value = {
      databaseId: row.databaseId,
      tableName: row.tableProp,
      dataSourceId: row.dataSourceId,
      id: row.id
    };

    // 根据数据源类型构建不同的参数
    let params;
    if (row.id) {
      // 本地文件类型数据源
      params = {
        sheetId: row.id,
        dataBaseId: -1,
        tableName: row.tableProp
      };
    } else {
      // 数据库类型数据源
      params = {
        dataBaseId: row.databaseId,
        tableName: row.tableProp
      };
    }

    // 获取字段详情
    const res = await getFieldDetail(params);

    if (res) {
      previewTableData.value = res;
      dialogVisible.value = true;
      dialogTitle.value = row.tableLabel;
      activeTab.value = 'field'; // 默认显示字段详情

      // 清空数据预览相关数据
      dataPreviewTableData.value = [];
      dataPreviewColumns.value = [];
    }
  } catch (error) {
    console.error('获取字段详情失败:', error);
    ElMessage.error('获取字段详情失败');
  }
};
const createDataSet = row => {
  console.log('创建数据集参数:', row);

  router.push({
    path: '/data/newdataset',
    query: {
      dataSourceId: row.dataSourceId,
      tableLabel: row.tableLabel,
      tableProp: row.tableProp
    }
  });
};
const createANewDataSource = () => {
  isShowcreateDataSet.value = true;
};
const collapseHandleChange = () => { };
const closeCreateDataSet = () => {
  isShowcreateDataSet.value = false;
  // 重新加载数据源列表
  getDataList();
};
const permissionClick = () => {
  if (permissionRef.value) {
    permissionRef.value.openDialog();
  }
};
const dataItems = item => {
  collapseActive.value = item.id;
  initialTip.value = true;
};

const getTagType = status => {
  switch (status) {
    case "正常":
      return 'primary';
    case "访问异常":
      return 'warning';
    case "字段变更":
      return 'danger';
    default:
      return 'info'; // 默认类型
  }
};

// 主表格列配置
const mainColumns = [
  {
    prop: 'tableProp',
    label: '数据表名称',
    width: '200',
  },
  {
    prop: 'tableLabel',
    label: '数据表标签',
    width: '200',
  },
  {
    prop: 'creator',
    label: '创建人',
  },
  {
    prop: 'updateTime',
    label: '更新时间',
    // formatter: (row) => formatDate(row.updateTime), // 可选：格式化时间
  },
  {
    prop: 'dataStatus',
    label: '数据状态',
    slot: true,
  },
  {
    prop: 'operation',
    label: '操作',
    slot: true,
    width: '200',
  },
];

// 预览数据表格列配置
const previewColumns = [
  {
    prop: 'COLUMN_NAME',
    label: '字段名称',
  },
  {
    prop: 'DATA_TYPE',
    label: '字段类型',
  },
  {
    prop: 'COLUMN_COMMENT',
    label: '字段描述',
  },
  {
    prop: 'DATA_LENGTH',
    label: '字段长度',
  },
];

const handleCurrentChange = (page) => {
  currentPage.value = page;
  fetchTableData(currentDataSourceId.value, page, pageSize.value);
};

const handleSizeChange = (size) => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchTableData(currentDataSourceId.value, 1, size);
};

const fetchTableData = (id, pageNo, pageSizeParam) => {
  const params = {
    pageNo,
    pageSize: -1// 设置为-1，表示不分页

  };

  // 如果有表格搜索关键词，添加到参数中
  const keyword = tableSearch.value.trim();
  if (keyword) {
    params.tableName = keyword;
  }

  getDataSourceWithTables(id, params).then(res => {
    if (res) {
      dataSourceDetail.value = res;
      const pageInfo = res.dataTablePageList || {};
      const processedData = (pageInfo.list || []).map(table => ({
        ...table, // 保留所有原始字段
        creator: res.creator,
        updateTime: formatDate(table.updateTime),
        databaseId: table.databaseId, // 添加数据源ID
      }));

      // 直接显示API返回的数据，不再需要前端过滤
      tableData.value = processedData;

      pageSize.value = pageInfo.pageSize || 10;
      total.value = pageInfo.total || 0;
    }
  });
};

// 编辑数据源
const editDataSource = async (item) => {
  // 判断数据源类型，如果是数据库类型，打开数据库编辑对话框
  if (mysqlList.value.some(db => db.id === item.id)) {
    // 这是数据库类型的数据源，获取详细信息并打开数据库编辑对话框
    try {
      // 使用新接口获取数据库详细配置信息
      const databaseConfig = await getDatabaseConfig(item.typeId);
      console.log('获取到的数据库详细配置:', databaseConfig);

      if (databaseConfig) {
        // 根据数据库类型设置对应的类型名称
        const typeMap = {
          1: 'Oracle',      // ORACLE
          2: 'Mysql',       // MYSQL
          3: 'PostgreSQL',  // POSTGRESQL
          4: '瀚高',        // HIGHGO
          5: 'Click House', // CLICKHOUSE
          6: '人大金仓',    // KINGBASE
          7: '达梦数据库',  // DM 达梦
          8: 'GaussDB'      // GAUSS 高斯
        };

        // 使用详细配置信息
        editDatabaseData.value = {
          ...databaseConfig,
          // 确保有基本字段
          id: databaseConfig.id || item.id,
          name: databaseConfig.datasourceName || item.name,
          type: databaseConfig.databaseType || item.type,
          databaseType: databaseConfig.databaseType || item.type,
          // 数据库连接信息
          datasourceName: databaseConfig.datasourceName || item.name,
          databaseIp: databaseConfig.databaseIp || '',
          databasePort: databaseConfig.databasePort || '',
          databaseInstance: databaseConfig.databaseInstance || '',
          databaseSchema: databaseConfig.databaseSchema || '',
          databaseUsername: databaseConfig.databaseUsername || '',
          databasePassword: '', // 密码不回显，安全考虑
          databaseVersion: databaseConfig.databaseVersion || '',
          // 添加原始数据源信息
          originalName: item.name,
          originalType: item.type,
          originalId: item.id
        };

        editDatabaseType.value = typeMap[databaseConfig.databaseType] || 'Mysql';
        console.log('原始 item 数据:', item);
        console.log('后端返回的 databaseConfig:', databaseConfig);
        console.log('设置的数据库类型:', editDatabaseType.value);
        console.log('类型映射结果:', databaseConfig.databaseType, '->', editDatabaseType.value);
        console.log('传递给编辑组件的数据:', editDatabaseData.value);

        // 确保数据设置完成后再显示对话框
        setTimeout(() => {
          editDatabaseDialogVisible.value = true;
          console.log('对话框已显示，状态:', {
            editDatabaseDialogVisible: editDatabaseDialogVisible.value,
            editDatabaseType: editDatabaseType.value,
            editDatabaseData: editDatabaseData.value
          });
        }, 100);
      }
    } catch (error) {
      console.error('获取数据库详情失败:', error);
      ElMessage.error('获取数据库详情失败：' + error.message);
    }
  } else {
    // 其他类型的数据源，使用原有的编辑方式
    editForm.id = item.id;
    editForm.name = item.name;
    editDialogVisible.value = true;
  }
};

// 确认删除数据源
const confirmDeleteDataSource = (item) => {
  ElMessageBox.confirm(
    '删除后15天可在回收站中恢复',
    `确认删除数据源"${item.name}"吗？`,

    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    handleDeleteDataSource(item.id, item.name);
  }).catch(() => {
    // 用户取消删除
  });
};

// 处理更新数据源
const handleUpdateDataSource = async () => {
  if (!editFormRef.value) return;

  await editFormRef.value.validate((valid) => {
    if (valid) {
      updateDataSource({
        id: editForm.id,
        name: editForm.name
      }).then(res => {
        if (res) {
          ElMessage.success('数据源更新成功');
          editDialogVisible.value = false;
          getDataList(); // 刷新列表
        } else {
          ElMessage.error('数据源更新失败');
        }
      }).catch(error => {
        ElMessage.error('更新过程中发生错误：' + error.message);
      });
    }
  });
};

// 获取数据库图标
const getDatabaseIcon = (dbType) => {
  // 导入图标
  const mysql = new URL('@/assets/imgs/datadempement/mysql.png', import.meta.url).href;
  const clickHouse = new URL('@/assets/imgs/datadempement/ClickHouse.png', import.meta.url).href;
  const gauss = new URL('@/assets/imgs/datadempement/gaoSi.png', import.meta.url).href;
  const dermal = new URL('@/assets/imgs/datadempement/dermal.png', import.meta.url).href;
  const kingbaseEs = new URL('@/assets/imgs/datadempement/renDajincang.png', import.meta.url).href;
  const postgresql = new URL('@/assets/imgs/datadempement/postgresql.png', import.meta.url).href;

  const iconMap = {
    'Mysql': mysql,
    'Click House': clickHouse,
    'GaussDB': gauss,
    '达梦数据库': dermal,
    '人大金仓': kingbaseEs,
    'PostgreSQL': postgresql
  };
  return iconMap[dbType] || mysql;
};

// 处理数据库更新成功
const handleDatabaseUpdateSuccess = () => {
  editDatabaseDialogVisible.value = false;
  getDataList(); // 刷新列表
  ElMessage.success('数据库更新成功');
};

// 清空编辑数据库的缓存数据
const clearEditDatabaseCache = () => {
  console.log('清空编辑数据库缓存数据');

  // 清空编辑相关数据
  editDatabaseData.value = null;
  editDatabaseType.value = '';
  editCollapseActive.value = ['2'];

  // 清空表格预览数据
  dataPreviewTableData.value = [];
  dataPreviewColumns.value = [];
  currentTableInfo.value = {
    tableName: '',
    tableComment: '',
    columns: []
  };

  // 重置tab状态
  activeTab.value = 'field';

  // 清空子组件引用
  if (editDatabaseRef.value) {
    editDatabaseRef.value = null;
  }

  console.log('缓存数据已清空');
};

// 关闭编辑数据库抽屉
const closeEditDatabaseDrawer = () => {
  editDatabaseDialogVisible.value = false;
  // 注意：这里不需要手动调用clearEditDatabaseCache，因为@closed事件会自动触发
};

// 处理删除数据源
const handleDeleteDataSource = async (id, dataSourceName) => {
  try {
    const res = await deleteDataSource(id);

    // 检查返回结果
    if (res && Array.isArray(res) && res.length > 0) {
      // 有关联的数据集，不能删除
      showRelatedDatasets(dataSourceName, res);
    } else if (res !== null && res !== undefined) {
      // 删除成功（返回空数组或其他成功标识）
      console.log('删除成功', res);
      ElMessage.success('数据源删除成功');
      getDataList(); // 刷新列表
      // 如果删除的是当前选中的数据源，清空右侧内容
      if (currentDataSourceId.value === id) {
        initialTip.value = false;
        collapseActive.value = '';
      }
    } else {
      ElMessage.error('数据源删除失败');
    }
  } catch (error) {
    console.error('删除过程中发生错误：', error);
    ElMessage.error('删除过程中发生错误：' + error.message);
  }
};

// 显示关联数据集信息的弹窗
const showRelatedDatasets = (dataSourceName, datasets) => {
  // 判断是否有数据集，决定弹窗宽度
  const hasDatasets = datasets && datasets.length > 0;
  const dialogWidth = hasDatasets ? '800px' : '400px';

  let content = `
    <div style="padding: 0;">
      <div style="margin-bottom: 16px; color: #E6A23C; display: flex; align-items: center;">
        <i class="el-icon-warning" style="margin-right: 8px; font-size: 16px;"></i>
        无法删除"${dataSourceName}"，存在${datasets.length}个相关的数据集
      </div>
      <div style="margin-bottom: 12px; font-size: 14px; color: #666;">
        删除会影响以下资源的使用（仅展示前10000条）
      </div>
  `;

  if (hasDatasets) {
    // 构建表格HTML
    const tableRows = datasets.map((dataset) => {
      const updateTime = dataset.updateTime ? formatDate(dataset.updateTime) : '2025/06/24 09:53:19';
      return `
        <tr style="border-bottom: 1px solid #ebeef5;">
          <td style="padding: 12px 8px; text-align: left; color: #409eff; cursor: pointer;">${dataset.name || '未命名'}</td>
          <td style="padding: 12px 8px; text-align: left; color: #606266;">${dataset.creator || dataset.updater || 'weimeiWS'}</td>
          <td style="padding: 12px 8px; text-align: left; color: #606266;">${dataset.updater || dataset.creator || 'weimeiWS'}</td>
          <td style="padding: 12px 8px; text-align: left; color: #606266;">${updateTime}</td>
        </tr>
      `;
    }).join('');

    content += `
      <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ebeef5; border-radius: 4px;">
        <table style="width: 100%; border-collapse: collapse; font-size: 14px;">
          <thead>
            <tr style="background-color: #f5f7fa; border-bottom: 1px solid #ebeef5;">
              <th style="padding: 12px 8px; text-align: left; font-weight: 500; color: #909399;">资源名称</th>
              <th style="padding: 12px 8px; text-align: left; font-weight: 500; color: #909399;">所有者</th>
              <th style="padding: 12px 8px; text-align: left; font-weight: 500; color: #909399;">修改人</th>
              <th style="padding: 12px 8px; text-align: left; font-weight: 500; color: #909399;">修改时间</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
      </div>
    `;
  }

  content += `</div>`;

  ElMessageBox.alert(
    content,
    '无法删除',
    {
      confirmButtonText: '确定',
      dangerouslyUseHTMLString: true,
      type: 'warning',
      customStyle: {
        width: dialogWidth,
        maxWidth: '90vw'
      }
    }
  );
};

// 处理tab切换
const handleTabClick = (tab) => {
  if (tab.name === 'field') {
    // 切换到数据预览时，如果还没有数据，则获取数据


  }
  fetchDataPreview();
};

// 获取数据预览
const fetchDataPreview = async () => {
  try {
    // 根据数据源类型构建不同的参数
    let params;
    if (currentTableInfo.value.id) {
      // 本地文件类型数据源
      params = {
        sheetId: currentTableInfo.value.id,
        dataBaseId: -1,
        pageNo: 1,
        pageSize: 10,
        tableName: currentTableInfo.value.tableName
      };
    } else {
      // 数据库类型数据源
      params = {
        dataBaseId: currentTableInfo.value.databaseId,
        pageNo: 1,
        pageSize: 10,
        tableName: currentTableInfo.value.tableName
      };
    }

    const res = await getDataDetail(params);

    if (res && res.list) {
      dataPreviewTableData.value = res.list;

      // 动态生成列配置
      if (res.list.length > 0) {
        const firstRow = res.list[0];
        dataPreviewColumns.value = Object.keys(firstRow).map(key => ({
          prop: key,
          label: key,
          width: '500'
        }));
      }
    }
  } catch (error) {
    console.error('获取数据预览失败:', error);
    ElMessage.error('获取数据预览失败');
  }
};

// 使用toRefs解构
// let { } = { ...toRefs(data) }
</script>
<style scoped lang="scss">
.app-container {
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgb(243, 245, 248);
  box-shadow: none;
  padding: 0;
}

.datasource-top-row {
  width: 100%;
  min-width: 1200px;
  height: 64px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  padding: 0 32px;
  box-sizing: border-box;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.datasource-header {
  font-size: 22px;
  font-weight: bold;
  text-align: left;
}

.datasource-main-content {
  flex: 1 1 0;
  display: flex;
  height: calc(100vh - 64px);
  overflow: hidden;
}

.left-content {
  display: flex;
  flex-direction: column;
  width: 360px;
  padding: 20px;
  margin-right: 10px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 1px 1px 2px 0px rgba(82, 90, 102, 0.04), 2px 2px 8px 0px rgba(82, 90, 102, 0.08);
  height: 100%;
  overflow-y: auto;
}

.right-content {
  width: calc(100% - 370px);
  display: flex;
  justify-content: center;
  position: relative;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 1px 1px 2px 0px rgba(82, 90, 102, 0.04), 2px 2px 8px 0px rgba(82, 90, 102, 0.08);
  height: 100%;
  overflow-y: auto;
}

.universal-font-color {
  color: #999999;
}

.app-container {
  .my-header {
    display: flex;
    justify-content: space-between;
  }
}

.left-content {
  .el-collapse {
    width: 90%;
    margin-top: 10px;
    flex: 1;
    overflow-y: auto;
    border: none;
    scrollbar-gutter: stable;

    /* 保证滚动条出现时不会改变布局 */
    :deep(.el-collapse-item) {
      border-radius: 10px !important;
      border: 1px solid #eee !important;
      margin-bottom: 10px;
      padding: 10px;
      background-color: #f5f7fa;
    }

    .source-icon {
      width: 20px;
      margin-right: 10px;
    }

    :deep(.el-collapse-item__header) {
      background-color: #f5f7fa;
      border: none;
    }

    :deep(.el-collapse-item__content) {
      background-color: #f5f7fa;
      padding-bottom: 0;
    }

    :deep(.el-collapse-item__wrap) {
      border: none;
      background-color: #f5f7fa;

      .collapseItem {
        width: 100%;
        margin: auto;
        padding: 15px;
        margin-top: 10px;
        border-radius: 10px;
        background: #fff;
        color: #666;
        border: 1px solid #fff;
        position: relative;
        cursor: pointer;

        .details-title {
          font-weight: 700;
          font-size: 14px;
        }

        .details {
          display: flex;
          justify-content: space-between;
          flex-wrap: nowrap;
          font-size: 12px;
        }

        .action-buttons {
          position: absolute;
          top: 8px;
          right: 8px;
          display: flex;
          gap: 8px;
          z-index: 10;

          .action-icon {
            width: 20px;
            height: 20px;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;

            &.edit-icon {
              color: #409eff;
              background-color: rgba(64, 158, 255, 0.1);

              &:hover {
                background-color: rgba(64, 158, 255, 0.2);
                transform: scale(1.1);
              }
            }

            &.delete-icon {
              color: #f56c6c;
              background-color: rgba(245, 108, 108, 0.1);

              &:hover {
                background-color: rgba(245, 108, 108, 0.2);
                transform: scale(1.1);
              }
            }
          }
        }
      }

      .isActive {
        border-color: #c6d0f7;
        background: #e7ecf9;
        color: #2744d6;
      }
    }
  }
}

.right-content {
  .newlyBuilt {
    width: 60%;
    height: 320px;
    border: 1px dashed #ccc;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    /* 水平和垂直居中 */
    .tipText {
      padding: 16px;
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      border-radius: 10px;
      font-size: 16px;

      .creat-new-data {
        font-size: 16px;
        color: #3b7bdf;
        padding: 0;
        border: none;
        cursor: pointer;
      }
    }
  }

  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 10px;

    .table-header-title {
      background: url('@/assets/imgs/datadempement/dataSource_bg1.png') no-repeat;
      background-size: 100% 100%;
      flex: 1;
      margin-right: 20px;
      border: 10px;
      display: flex;
      flex-direction: column;
      padding: 20px;
      color: #fff;
      border-radius: 10px;

      p {
        margin: 0;
      }

      p:nth-child(1) {
        font-weight: 700;
        margin-bottom: 20px;
      }
    }
  }
}

/* 新建数据集弹出框动画 */
.slide-enter-active {
  animation: slide-in 0.3s ease-out;
}

.slide-leave-active {
  animation: slide-out 0.3s ease-in;
}

@keyframes slide-in {
  from {
    transform: translateY(100%);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-out {
  from {
    transform: translateY(0);
    opacity: 1;
  }

  to {
    transform: translateY(100%);
    opacity: 0;
  }
}






.database-edit-content {
  display: flex;
  height: calc(80vh - 60px);
  background: #fff;
  min-height: 200px;
  gap: 20px;
}

.database-edit-content .leftList {
  width: 25%;
  border-right: 1px solid #e4e7ed;
  padding: 20px;
  background: #f8f9fa;
  overflow: visible;
  flex-shrink: 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.database-edit-content .leftList .el-collapse {
  border: none;
  background: transparent;
}

.database-edit-content .leftList .el-collapse-item {
  border: none;
  margin-bottom: 8px;
}

.database-edit-content .leftList .el-collapse-item :deep(.el-collapse-item__header) {
  background: transparent;
  border: none;
  font-weight: 600;
  color: #303133;
  font-size: 16px;
  padding: 12px 0;
}

.database-edit-content .leftList .collapseItem {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 8px 0;
  border-radius: 8px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #606266;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.database-edit-content .leftList .collapseItem.isActive {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  color: #1890ff;
  border: 1px solid #91d5ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
  transform: translateY(-1px);
}

.database-edit-content .leftList .collapseItem.disabled {
  background-color: #f5f7fa;
  color: #909399;
  cursor: not-allowed;
  opacity: 0.6;
  border: 1px solid #e4e7ed;
}

.database-edit-content .leftList .collapseItem img {
  margin-right: 8px;
}

/* 表单区域样式优化 */
.database-edit-content .chiropractic :deep(.el-form) {
  background: transparent;
}

.database-edit-content .chiropractic :deep(.el-form-item) {
  margin-bottom: 24px;
}

.database-edit-content .chiropractic :deep(.el-form-item__label) {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  line-height: 1.5;
}

.database-edit-content .chiropractic :deep(.el-input),
.database-edit-content .chiropractic :deep(.el-select) {
  border-radius: 6px;
}

.database-edit-content .chiropractic :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #dcdfe6;
  transition: all 0.3s;
}

.database-edit-content .chiropractic :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc;
}

.database-edit-content .chiropractic :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409eff;
}

.database-edit-content .chiropractic :deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s;
}

.database-edit-content .chiropractic :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.database-edit-content .chiropractic :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b3ff 0%, #409eff 100%);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .database-edit-content {
    flex-direction: column;
    gap: 15px;
  }

  .database-edit-content .leftList {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;
    padding: 15px;
  }

  .database-edit-content .chiropractic {
    width: 100%;
    padding: 20px;
  }
}

/* 抽屉头部样式优化 */
.drawer-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #e4e7ed;
}

.drawer-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.drawer-close {
  margin-left: 20px;
  border-radius: 6px;
  transition: all 0.3s;
}

.drawer-close:hover {
  background: #f56c6c;
  border-color: #f56c6c;
  color: #fff;
  transform: translateY(-1px);
}

.database-edit-content .chiropractic {
  width: 70%;
  padding: 30px;
  overflow-y: auto;
  position: relative;
  height: 100%;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
}
</style>
